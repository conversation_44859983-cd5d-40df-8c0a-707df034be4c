<?php
// Load Composer dependencies.
require_once __DIR__ . '/vendor/autoload.php';
Timber\Timber::init();
/**
 * Vérifie que Timber est chargé et disponible
 */
if (!class_exists('Timber')) {
    add_action('admin_notices', function() {
        echo '<div class="error"><p>Timber n\'est pas activé. Veuillez l\'activer dans <a href="' . esc_url(admin_url('plugins.php#timber')) . '">' . esc_url(admin_url('plugins.php')) . '</a></p></div>';
    });

    add_filter('template_include', function($template) {
        return get_stylesheet_directory() . '/static/no-timber.html';
    });

    return;
}

/**
 * Configuration des dossiers pour les fichiers .twig
 */
Timber::$dirname = array('templates', 'views');

/**
 * Désactive l'échappement automatique de Twig
 */
Timber::$autoescape = false;

/**
 * Configuration du thème
 */
class StarterSite extends Timber\Site {
    public function __construct() {
        add_action('after_setup_theme', array($this, 'theme_supports'));
        add_filter('timber/context', array($this, 'add_to_context'));
        add_filter('timber/twig', array($this, 'add_to_twig'));
        parent::__construct();
    }

    /**
     * Ajoute les variables contextuelles pour Timber
     */
    public function add_to_context($context) {
        // S'assure que Polylang est chargé
        if (function_exists('pll_current_language')) {
            $current_language = pll_current_language();

            // Définit le menu selon la langue
            switch ($current_language) {
                case 'en':
                    $menu_name = 'mainMenuEN';
                    $footer_menu_name = 'footerEN';
                    break;
                case 'es':
                    $menu_name = 'mainMenuES';
                    $footer_menu_name = 'footerES';
                    break;
                case 'pt':
                    $menu_name = 'mainMenuPT';
                    $footer_menu_name = 'footerPT';
                    break;
                case 'fr':
                default:
                    $menu_name = 'mainMenuFR';
                    $footer_menu_name = 'footerFR';
                    break;
            }

            $context['menu'] =  Timber::get_menu($menu_name);
            $context['footer_menu'] =  Timber::get_menu($footer_menu_name);
            $context['current_language'] = $current_language;

            // Récupère les traductions disponibles pour l'article courant
            $post_id = get_the_ID();
            $translations = pll_get_post_translations($post_id);

            // Récupère toutes les langues disponibles
            $all_languages = pll_the_languages(['raw' => 1, 'hide_if_empty' => 0]);

            // Filtre les langues pour n'afficher que celles avec une traduction
            $available_languages = [];
            foreach ($all_languages as $lang) {
                if (isset($translations[$lang['slug']])) {
                    $available_languages[] = $lang;
                }
            }

            $context['languages'] = $available_languages;

        } else {
            // Menu par défaut si Polylang n'est pas actif
            $context['menu'] =  Timber::get_menu('mainMenuEN');
            $context['footer_menu'] =  Timber::get_menu('footerEN');
            $context['current_language'] = 'en';
            $context['languages'] = [];
        }

        return $context;
    }

    /**
     * Configure les fonctionnalités du thème
     */
    public function theme_supports() {
        add_theme_support('automatic-feed-links');
        add_theme_support('title-tag');
        add_theme_support('post-thumbnails');
        add_theme_support('html5', array('gallery', 'caption'));
    }

    /**
     * Ajoute des extensions à Twig
     */
    public function add_to_twig($twig) {
        $twig->addExtension(new Twig\Extension\StringLoaderExtension());
        return $twig;
    }
}

function themealmasi_enqueue_assets() {
    // Enqueue scripts
    wp_enqueue_script('themealmasi-script', get_template_directory_uri() . '/scripts/script.js', [], null, true);

    // Enqueue styles
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Barlow:wght@400;500;600;700&display=swap', false);
}
add_action('wp_enqueue_scripts', 'themealmasi_enqueue_assets');

/**
 * Génère le fil d'Ariane
 */
function get_breadcrumb() {
    $separator = '<span class="breadcrumb-separator" aria-hidden="true">›</span>';
    $breadcrumb = '<a href="' . home_url() . '" rel="nofollow">' . pll__('Home') . '</a>';

    if (is_category()) {
        $category = get_queried_object();
        $breadcrumb .= $separator . '<span>' . single_cat_title('', false) . '</span>';
    } elseif (is_single()) {
        $categories = get_the_category();
        if (!empty($categories)) {
            $category_links = array();
            foreach ($categories as $category) {
                $category_links[] = '<a href="' . get_category_link($category->term_id) . '">' . $category->name . '</a>';
            }
            $breadcrumb .= $separator . implode($separator, $category_links);
        }
        $breadcrumb .= $separator . '<span>' . get_the_title() . '</span>';
    } elseif (is_page()) {
        global $post;
        $breadcrumb .= get_page_parents_breadcrumb($post, $separator);
    } elseif (is_search()) {
        $breadcrumb .= $separator . '<span>' . sprintf(pll__('Search Results for: %s'), '<em>' . get_search_query() . '</em>') . '</span>';
    } elseif ((isset($_GET['na']) && $_GET['na'] === 'view' && isset($_GET['id'])) ||
              (isset($_GET['action']) && $_GET['action'] === 'view_newsletter' && isset($_GET['newsletter_id']))) {
        // Breadcrumb pour les newsletters
        $breadcrumb .= $separator . '<a href="' . home_url('/category/news-and-events/') . '">' . pll__('News and Events') . '</a>';
        $breadcrumb .= $separator . '<span>' . pll__('Newsletter') . '</span>';
    }

    return $breadcrumb;
}

function get_page_parents_breadcrumb($post, $separator) {
    $breadcrumb = '';
    $parent_id = $post->post_parent;
    $breadcrumb_links = [];

    while ($parent_id) {
        $page = get_post($parent_id);
        $breadcrumb_links[] = '<a href="' . get_permalink($page->ID) . '">' . get_the_title($page->ID) . '</a>';
        $parent_id = $page->post_parent;
    }

    if (!empty($breadcrumb_links)) {
        $breadcrumb_links = array_reverse($breadcrumb_links);
        $breadcrumb .= $separator . implode($separator, $breadcrumb_links);
    }

    $breadcrumb .= $separator . '<span>' . get_the_title($post->ID) . '</span>';

    return $breadcrumb;
}

// Message validation envoie mail contact
add_filter('wpcf7_ajax_json_echo', function($response, $result) {
    if ($result['status'] === 'mail_sent') {
        $current_lang = pll_current_language();

        // Définir les messages selon la langue
        $messages = array(
            'fr' => 'Votre message a été envoyé avec succès !',
            'en' => 'Your message has been sent successfully!',
            'es' => '¡Su mensaje ha sido enviado con éxito!',
            'pt' => 'Sua mensagem foi enviada com sucesso!'
        );

        // Définir le message par défaut en anglais si la langue n'est pas trouvée
        $success_message = isset($messages[$current_lang]) ? $messages[$current_lang] : $messages['en'];

        $response['message'] = $success_message;
    }
    return $response;
}, 10, 2);

new StarterSite();

/**
 * Gestion des newsletters - Affichage individuel
 */
function handle_newsletter_view() {
    // Gérer les URLs du plugin Newsletter (?na=view&id=X)
    if (isset($_GET['na']) && $_GET['na'] === 'view' && isset($_GET['id'])) {
        $newsletter_id = intval($_GET['id']);

        if (class_exists('Newsletter')) {
            global $wpdb;
            $newsletter_table = $wpdb->prefix . 'newsletter';

            $newsletter = $wpdb->get_row($wpdb->prepare(
                "SELECT id, subject, message, created, status
                 FROM {$newsletter_table}
                 WHERE id = %d AND status = 'sent'",
                $newsletter_id
            ));

            if ($newsletter) {
                // Créer le contexte pour Timber
                $context = Timber::context();
                $context['newsletter'] = array(
                    'id' => $newsletter->id,
                    'title' => $newsletter->subject,
                    'content' => $newsletter->message,
                    'date' => date('d/m/Y', strtotime($newsletter->created)),
                    'raw_date' => $newsletter->created
                );

                // Fil d'Ariane
                $context['breadcrumbs'] = function_exists('get_breadcrumb') ? get_breadcrumb() : '';

                // Rendre le template
                Timber::render('newsletter-single.twig', $context);
                exit;
            }
        }
    }

    // Gérer aussi l'ancien format pour compatibilité
    if (isset($_GET['action']) && $_GET['action'] === 'view_newsletter' && isset($_GET['newsletter_id'])) {
        $newsletter_id = intval($_GET['newsletter_id']);

        if (class_exists('Newsletter')) {
            global $wpdb;
            $newsletter_table = $wpdb->prefix . 'newsletter';

            $newsletter = $wpdb->get_row($wpdb->prepare(
                "SELECT id, subject, message, created, status
                 FROM {$newsletter_table}
                 WHERE id = %d AND status = 'sent'",
                $newsletter_id
            ));

            if ($newsletter) {
                // Créer le contexte pour Timber
                $context = Timber::context();
                $context['newsletter'] = array(
                    'id' => $newsletter->id,
                    'title' => $newsletter->subject,
                    'content' => $newsletter->message,
                    'date' => date('d/m/Y', strtotime($newsletter->created)),
                    'raw_date' => $newsletter->created
                );

                // Fil d'Ariane
                $context['breadcrumbs'] = function_exists('get_breadcrumb') ? get_breadcrumb() : '';

                // Rendre le template
                Timber::render('newsletter-single.twig', $context);
                exit;
            }
        }
    }
}
add_action('template_redirect', 'handle_newsletter_view');

/**
 * Ajouter les traductions pour les newsletters
 */
function add_newsletter_translations() {
    if (function_exists('pll_register_string')) {
        pll_register_string('newsletter', 'Newsletter', 'Theme Almasi');
        pll_register_string('newsletter_archive', 'Newsletter Archive', 'Theme Almasi');
        pll_register_string('back_to_newsletters', 'Back to Newsletters', 'Theme Almasi');
    }
}
add_action('init', 'add_newsletter_translations');

/**
 * Créer une page pour les archives de newsletter si elle n'existe pas
 */
function create_newsletter_archive_page() {
    $page_title = 'Newsletter Archives';
    $page_slug = 'newsletter-archives';
    
    // Vérifier si la page existe déjà
    $existing_page = get_page_by_path($page_slug);
    
    if (!$existing_page) {
        // Créer la page
        $page_data = array(
            'post_title' => $page_title,
            'post_name' => $page_slug,
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_content' => '[newsletter_archive /]',
            'page_template' => 'page-newsletter-archive.php'
        );
        
        $page_id = wp_insert_post($page_data);
        
        if ($page_id) {
            // Ajouter un message de succès
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success is-dismissible"><p>Page "Newsletter Archives" créée avec succès !</p></div>';
            });
        }
    }
}
add_action('admin_init', 'create_newsletter_archive_page');

/**
 * Fonction de débogage temporaire pour les newsletters
 */
function debug_newsletters() {
    if (isset($_GET['debug_newsletters']) && current_user_can('manage_options')) {
        if (class_exists('Newsletter')) {
            global $wpdb;
            
            echo '<h2>Debug Newsletters</h2>';
            
            // Vérifier les tables
            $tables = array(
                $wpdb->prefix . 'newsletter',
                $wpdb->prefix . 'newsletter_emails',
                $wpdb->prefix . 'newsletter_sent'
            );
            
            foreach ($tables as $table) {
                $exists = $wpdb->get_var("SHOW TABLES LIKE '{$table}'");
                echo "<p>Table {$table}: " . ($exists ? 'EXISTE' : 'N\'EXISTE PAS') . "</p>";
                
                if ($exists) {
                    $count = $wpdb->get_var("SELECT COUNT(*) FROM {$table}");
                    echo "<p>Nombre d'enregistrements: {$count}</p>";
                    
                    if ($count > 0) {
                        $sample = $wpdb->get_row("SELECT * FROM {$table} LIMIT 1");
                        echo "<pre>Sample: " . print_r($sample, true) . "</pre>";
                    }
                }
            }
        } else {
            echo '<p>Plugin Newsletter non trouvé</p>';
        }
        exit;
    }
}
add_action('init', 'debug_newsletters');