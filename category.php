<?php
/**
 * Template pour afficher les articles d'une catégorie
 */

$context = Timber::context();

// Récupérer les catégories "news-and-events" et "blog"
$news_events_cat = get_category_by_slug('news-and-events');
$blog_cat = get_category_by_slug('blog');

// Récupérer tous les articles de ces catégories
$args = array(
    'post_type' => 'post',
    'post_status' => 'publish',
    'posts_per_page' => -1,
    'tax_query' => array(
        'relation' => 'OR',
        array(
            'taxonomy' => 'category',
            'field' => 'slug',
            'terms' => array('news-and-events', 'blog')
        )
    ),
    'orderby' => 'date',
    'order' => 'DESC'
);

$context['all_posts'] = Timber::get_posts($args);

// Récupérer les archives de newsletter via le shortcode
$context['newsletters'] = array();

if (shortcode_exists('newsletter_archive')) {
    // Utiliser le shortcode pour récupérer les archives
    $newsletter_archive_html = do_shortcode('[newsletter_archive /]');

    // Extraire les liens d'archive du HTML
    $dom = new DOMDocument();
    // Pour éviter les warnings sur le HTML fragment
    libxml_use_internal_errors(true);
    $dom->loadHTML('<?xml encoding="utf-8" ?>' . $newsletter_archive_html);
    libxml_clear_errors();

    $links = $dom->getElementsByTagName('a');
    foreach ($links as $link) {
        $context['newsletters'][] = array(
            'id' => 'newsletter-' . md5($link->getAttribute('href')),
            'title' => $link->nodeValue,
            'content' => '', // Pas de contenu, juste le titre
            'date' => '', // Si tu as la date, tu peux l'ajouter ici
            'link' => $link->getAttribute('href'),
            'type' => 'newsletter'
        );
    }
}

// Récupérer les catégories pour le filtrage
$context['filter_categories'] = array();

// Récupérer toutes les catégories qui ont des posts
$categories = get_categories(array(
    'hide_empty' => true,
    'orderby' => 'name',
    'order' => 'ASC'
));

foreach ($categories as $category) {
    // Inclure seulement les catégories pertinentes
    if (in_array($category->slug, array('news-and-events', 'blog', 'news', 'events'))) {
        $context['filter_categories'][] = array(
            'id' => $category->term_id,
            'name' => $category->name,
            'slug' => $category->slug,
            'count' => $category->count
        );
    }
}

// Si aucune catégorie trouvée, utiliser les catégories spécifiques
if (empty($context['filter_categories'])) {
    if ($news_events_cat) {
        $context['filter_categories'][] = array(
            'id' => $news_events_cat->term_id,
            'name' => $news_events_cat->name,
            'slug' => $news_events_cat->slug,
            'count' => $news_events_cat->count
        );
    }
    if ($blog_cat) {
        $context['filter_categories'][] = array(
            'id' => $blog_cat->term_id,
            'name' => $blog_cat->name,
            'slug' => $blog_cat->slug,
            'count' => $blog_cat->count
        );
    }
}

// Ajouter l'option newsletter (toujours afficher)
$context['filter_categories'][] = array(
    'id' => 'newsletter',
    'name' => 'Newsletter',
    'slug' => 'newsletter',
    'count' => count($context['newsletters'])
);

// Gérer le filtrage côté serveur si des paramètres sont envoyés
$selected_categories = isset($_GET['categories']) ? $_GET['categories'] : array();
if (!is_array($selected_categories)) {
    $selected_categories = array($selected_categories);
}

// Convertir les IDs de catégories en chaînes pour la comparaison
$selected_categories = array_map('strval', $selected_categories);

// Combiner les posts et newsletters selon les filtres
$all_content = array();

// Ajouter les newsletters si aucun filtre ou si newsletter est sélectionné
if (empty($selected_categories) || in_array('newsletter', $selected_categories)) {
    foreach ($context['newsletters'] as $newsletter) {
        $all_content[] = $newsletter;
    }
}

// Ajouter les posts selon les filtres
if (empty($selected_categories)) {
    // Aucun filtre : afficher tous les posts
    $filtered_posts = $context['all_posts'];
} else {
    // Filtres actifs : filtrer les posts selon les catégories sélectionnées
    $category_ids = array_filter($selected_categories, function($cat) {
        return $cat !== 'newsletter' && is_numeric($cat);
    });

    if (!empty($category_ids)) {
        $filtered_args = array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'tax_query' => array(
                array(
                    'taxonomy' => 'category',
                    'field' => 'term_id',
                    'terms' => $category_ids
                )
            ),
            'orderby' => 'date',
            'order' => 'DESC'
        );
        $filtered_posts = Timber::get_posts($filtered_args);
    } else {
        // Seulement newsletter sélectionné, pas de posts
        $filtered_posts = array();
    }
}

// Ajouter les posts au contenu
if (!empty($filtered_posts)) {
    foreach ($filtered_posts as $post) {
        $all_content[] = array(
            'id' => $post->ID,
            'title' => $post->title,
            'content' => $post->excerpt ?: wp_trim_words(strip_tags($post->content), 20, '...'),
            'date' => $post->date('d/m/Y'),
            'link' => $post->link,
            'thumbnail' => $post->thumbnail,
            'categories' => $post->categories,
            'type' => 'post'
        );
    }
}

// Trier par date (plus récent en premier)
usort($all_content, function($a, $b) {
    $date_a = strtotime(str_replace('/', '-', $a['date']));
    $date_b = strtotime(str_replace('/', '-', $b['date']));
    return $date_b - $date_a;
});

$context['posts'] = $all_content;

// Catégorie en cours
$context['category'] = get_queried_object();
$context['category_title'] = single_cat_title('', false);
$context['category_description'] = category_description();

// Fil d'Ariane (breadcrumb personnalisé si défini)
$context['breadcrumbs'] = function_exists('get_breadcrumb') ? get_breadcrumb() : '';

// Traductions Polylang si disponible
$translations = [];
if (function_exists('pll_get_term_translations')) {
    $translations = pll_get_term_translations($context['category']->term_id);
}
$context['category_translations'] = $translations;

// Catégories sélectionnées pour le template
$context['selected_categories'] = $selected_categories;

Timber::render('category.twig', $context);
