<?php
/**
 * Template pour afficher les articles d'une catégorie
 */

$context = Timber::context();

// Récupérer les catégories "news-and-events" et "blog"
$news_events_cat = get_category_by_slug('news-and-events');
$blog_cat = get_category_by_slug('blog');

// Récupérer tous les articles de ces catégories
$args = array(
    'post_type' => 'post',
    'post_status' => 'publish',
    'posts_per_page' => -1,
    'tax_query' => array(
        'relation' => 'OR',
        array(
            'taxonomy' => 'category',
            'field' => 'slug',
            'terms' => array('news-and-events', 'blog')
        )
    ),
    'orderby' => 'date',
    'order' => 'DESC'
);

$context['all_posts'] = Timber::get_posts($args);

// Récupérer les archives de newsletter via le shortcode
$context['newsletters'] = array();

if (shortcode_exists('newsletter_archive')) {
    // Utiliser le shortcode pour récupérer les archives
    $newsletter_archive_html = do_shortcode('[newsletter_archive /]');

    // Extraire les liens d'archive du HTML
    $dom = new DOMDocument();
    // Pour éviter les warnings sur le HTML fragment
    libxml_use_internal_errors(true);
    $dom->loadHTML('<?xml encoding="utf-8" ?>' . $newsletter_archive_html);
    libxml_clear_errors();

    $links = $dom->getElementsByTagName('a');
    foreach ($links as $link) {
        $original_url = $link->getAttribute('href');

        // Extraire l'ID de la newsletter depuis l'URL originale
        $newsletter_id = null;
        if (preg_match('/[?&]id=(\d+)/', $original_url, $matches)) {
            $newsletter_id = $matches[1];
        }

        // Créer un lien personnalisé vers notre page de newsletter
        $custom_link = $newsletter_id ? home_url('/newsletter-view/?newsletter_id=' . $newsletter_id) : $original_url;

        $context['newsletters'][] = array(
            'id' => 'newsletter-' . ($newsletter_id ?: md5($original_url)),
            'title' => $link->nodeValue,
            'content' => '', // Pas de contenu, juste le titre
            'date' => '', // Si tu as la date, tu peux l'ajouter ici
            'link' => $custom_link,
            'original_link' => $original_url,
            'newsletter_id' => $newsletter_id,
            'type' => 'newsletter'
        );
    }
}

// Récupérer les catégories pour le filtrage
$context['filter_categories'] = array();
if ($news_events_cat) {
    $context['filter_categories'][] = array(
        'id' => $news_events_cat->term_id,
        'name' => $news_events_cat->name,
        'slug' => $news_events_cat->slug,
        'count' => $news_events_cat->count
    );
}
if ($blog_cat) {
    $context['filter_categories'][] = array(
        'id' => $blog_cat->term_id,
        'name' => $blog_cat->name,
        'slug' => $blog_cat->slug,
        'count' => $blog_cat->count
    );
}

// Ajouter l'option newsletter (toujours afficher pour le débogage)
$context['filter_categories'][] = array(
    'id' => 'newsletter',
    'name' => 'Newsletter',
    'slug' => 'newsletter',
    'count' => count($context['newsletters'])
);

// Gérer le filtrage côté serveur si des paramètres sont envoyés
$selected_categories = isset($_GET['categories']) ? $_GET['categories'] : array();
if (!is_array($selected_categories)) {
    $selected_categories = array($selected_categories);
}

// Combiner les posts et newsletters selon les filtres
$all_content = array();

if (empty($selected_categories) || in_array('newsletter', $selected_categories)) {
    // Ajouter les newsletters
    foreach ($context['newsletters'] as $newsletter) {
        $all_content[] = $newsletter;
    }
}

if (empty($selected_categories) || !in_array('newsletter', $selected_categories)) {
    // Ajouter les posts filtrés
    if (!empty($selected_categories) && !in_array('newsletter', $selected_categories)) {
        $filtered_args = array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'tax_query' => array(
                array(
                    'taxonomy' => 'category',
                    'field' => 'term_id',
                    'terms' => array_filter($selected_categories, function($cat) {
                        return $cat !== 'newsletter';
                    })
                )
            ),
            'orderby' => 'date',
            'order' => 'DESC'
        );
        $filtered_posts = Timber::get_posts($filtered_args);
    } else {
        $filtered_posts = $context['all_posts'];
    }

    foreach ($filtered_posts as $post) {
        $all_content[] = array(
            'id' => $post->ID,
            'title' => $post->title,
            'content' => $post->excerpt ?: wp_trim_words(strip_tags($post->content), 20, '...'),
            'date' => $post->date('d/m/Y'),
            'link' => $post->link,
            'thumbnail' => $post->thumbnail,
            'categories' => $post->categories,
            'type' => 'post'
        );
    }
}

// Trier par date (plus récent en premier)
usort($all_content, function($a, $b) {
    $date_a = strtotime(str_replace('/', '-', $a['date']));
    $date_b = strtotime(str_replace('/', '-', $b['date']));
    return $date_b - $date_a;
});

$context['posts'] = $all_content;

// Catégorie en cours
$context['category'] = get_queried_object();
$context['category_title'] = single_cat_title('', false);
$context['category_description'] = category_description();

// Fil d'Ariane (breadcrumb personnalisé si défini)
$context['breadcrumbs'] = function_exists('get_breadcrumb') ? get_breadcrumb() : '';

// Traductions Polylang si disponible
$translations = [];
if (function_exists('pll_get_term_translations')) {
    $translations = pll_get_term_translations($context['category']->term_id);
}
$context['category_translations'] = $translations;

// Catégories sélectionnées pour le template
$context['selected_categories'] = $selected_categories;

Timber::render('category.twig', $context);
