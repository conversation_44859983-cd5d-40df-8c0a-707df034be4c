{% extends "base.twig" %}

{% block content %}
    {# Section articles dans un wrapper #}
    <div class="wrapper">
        <section class="articles-section">
            <h2 class="section-title">Actualités récentes</h2>
            <div class="posts-grid">
                {% for post in posts %}
                    <article class="tease tease-post {% if not post.thumbnail %}no-image{% endif %}" id="tease-{{ post.ID }}">
                        <a href="{{ post.link }}">
                            {% if post.thumbnail %}
                                <img src="{{ post.thumbnail }}" alt="{{ post.title }}" class="article-thumbnail">
                            {% endif %}
                            <h2 class="article-h2">{{ post.title }}</h2>
                            {% if post.excerpt %}
                                <div class="actualite-excerpt">
                                    {{ post.excerpt|slice(0, 150) }}{% if post.excerpt|length > 150 %}...{% endif %}
                                </div>
                            {% endif %}
                        </a>
                    </article>
                {% else %}
                    <p>Aucun article trouvé dans la catégorie "actualites".</p>
                {% endfor %}
            </div>
            <div class="view-all-button-container">
                <a href="/category/actualites" class="view-all-button">Voir toutes les actualités</a>
            </div>
        </section>
    </div>
{% endblock %}
