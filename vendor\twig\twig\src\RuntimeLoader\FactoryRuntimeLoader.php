<?php

/*
 * This file is part of Twig.
 *
 * (c) <PERSON><PERSON><PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Twig\RuntimeLoader;

/**
 * <PERSON><PERSON> loads the runtime implementations for a Twig element.
 *
 * <AUTHOR> <<EMAIL>>
 */
class FactoryRuntimeLoader implements RuntimeLoaderInterface
{
    /**
     * @param array $map An array where keys are class names and values factory callables
     */
    public function __construct(
        private array $map = [],
    ) {
    }

    public function load(string $class)
    {
        if (!isset($this->map[$class])) {
            return null;
        }

        $runtimeFactory = $this->map[$class];

        return $runtimeFactory();
    }
}
