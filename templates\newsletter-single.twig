{% extends "base.twig" %}

{% block content %}
  {# Fil d'Ariane (si disponible) #}
  {% if breadcrumbs %}
    <div class="breadcrumb">{{ breadcrumbs|raw }}</div>
  {% endif %}

  {# Bouton retour #}
  <div class="wrapper">
    <a href="{{ function('home_url') }}/category/news-and-events/" class="back-link">
      {% if current_language == 'fr' %}
        ← Retour aux actualités
      {% elseif current_language == 'en' %}
        ← Back to news
      {% elseif current_language == 'es' %}
        ← Volver a las noticias
      {% elseif current_language == 'pt' %}
        ← Voltar às notícias
      {% endif %}
    </a>
  </div>

  {# Contenu de la newsletter #}
  <section class="newsletter-single">
    <div class="wrapper">
      <article class="newsletter-content">
        <header class="newsletter-header">
          <div class="newsletter-meta">
            <span class="newsletter-date">{{ newsletter.date }}</span>
            <span class="newsletter-tag">Newsletter</span>
          </div>
          <h1 class="newsletter-title">{{ newsletter.title }}</h1>
        </header>

        <div class="newsletter-body">
          {{ newsletter.content|raw }}
        </div>

        <footer class="newsletter-footer">
          <p class="newsletter-info">
            {% if current_language == 'fr' %}
              Cette newsletter a été envoyée le {{ newsletter.date }}.
            {% elseif current_language == 'en' %}
              This newsletter was sent on {{ newsletter.date }}.
            {% elseif current_language == 'es' %}
              Este boletín fue enviado el {{ newsletter.date }}.
            {% elseif current_language == 'pt' %}
              Este boletim foi enviado em {{ newsletter.date }}.
            {% endif %}
          </p>
        </footer>
      </article>
    </div>
  </section>
{% endblock %} 