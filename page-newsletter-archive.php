<?php
/**
 * Template pour la page des archives de newsletter
 */

$context = Timber::context();

// Titre de la page
$context['page_title'] = get_the_title();
$context['page_content'] = get_the_content();

// Fil d'Ariane
$context['breadcrumbs'] = function_exists('get_breadcrumb') ? get_breadcrumb() : '';

// Récupérer les archives de newsletter via le shortcode
if (shortcode_exists('newsletter_archive')) {
    $context['newsletter_archive_html'] = do_shortcode('[newsletter_archive /]');
} else {
    $context['newsletter_archive_html'] = '<p>Aucune archive de newsletter disponible.</p>';
}

Timber::render('page-newsletter-archive.twig', $context); 