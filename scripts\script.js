document.addEventListener('DOMContentLoaded', function() {
    // Sélectionne toutes les balises <ul> dans .nav-main.open
    var subMenus = document.querySelectorAll('.nav-main ul');

    // Boucle pour inspecter et cacher les <ul> vides
    subMenus.forEach(function(subMenu) {
        // Vérifie si la liste <ul> n'a pas de nœuds enfants de type élément
        const hasChildElements = Array.from(subMenu.children).some(child => child.nodeType === 1);
        if (!hasChildElements) {
            subMenu.style.display = 'none'; // Cache les <ul> vides
        }
    });

    // Sélectionne le sélecteur de langue et son bouton de bascule
    const languageSwitcher = document.querySelector('.language-switcher');
    const languageToggle = document.querySelector('.language-switcher-toggle');
    const languageDropdown = document.querySelector('.language-dropdown');

    // S'assurer que le dropdown est caché au chargement
    if (languageDropdown) {
        languageDropdown.style.display = 'none';
    }

    // Initialiser l'état du sélecteur de langue
    if (languageSwitcher) {
        languageSwitcher.classList.remove('open');
    }

    if (languageToggle) {

        languageToggle.setAttribute('aria-expanded', 'false');
    }

    // Ajoute un événement de clic au bouton de bascule pour ouvrir/fermer le sélecteur de langue
    if (languageToggle && languageSwitcher && languageDropdown) {
        languageToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Basculer la classe open
            const isOpen = languageSwitcher.classList.contains('open');

            if (isOpen) {
                // Fermer le menu
                languageSwitcher.classList.remove('open');
                languageToggle.setAttribute('aria-expanded', 'false');
                languageDropdown.style.display = 'none';
            } else {
                // Ouvrir le menu
                languageSwitcher.classList.add('open');
                languageToggle.setAttribute('aria-expanded', 'true');
                languageDropdown.style.display = 'block';
            }
        });

        // Ferme le dropdown si on clique en dehors
        document.addEventListener('click', function(e) {
            if (languageSwitcher.classList.contains('open') && !languageSwitcher.contains(e.target)) {
                languageSwitcher.classList.remove('open');
                languageToggle.setAttribute('aria-expanded', 'false');
                languageDropdown.style.display = 'none';
            }
        });
    }

    // Menu burger
    const burgerMenu = document.querySelector('.burger-menu');
    const navLinks = document.querySelector('.nav-links');

    if (burgerMenu && navLinks) {
        // Fonction pour ouvrir/fermer le menu
        function toggleMenu() {
            navLinks.classList.toggle('active');
            burgerMenu.classList.toggle('active');

            // Ajouter une classe au body pour empêcher le défilement quand le menu est ouvert
            if (navLinks.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
                document.body.style.overflowX = 'hidden';

                const firstLink = navLinks.querySelector('a');
                if (firstLink) firstLink.focus();
            } else {
                document.body.style.overflow = '';
                document.body.style.overflowX = '';

                // Fermer tous les sous-menus ouverts quand on ferme le menu principal
                document.querySelectorAll('.nav-item.open, .nav-dropdown-item.open, .nav-sub-dropdown-item.open').forEach(item => {
                    item.classList.remove('open');
                });
            }

            // Après le toggleMenu()
            if (burgerMenu) {
                burgerMenu.setAttribute('aria-expanded', navLinks.classList.contains('active') ? 'true' : 'false');
            }
        }

        burgerMenu.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleMenu();
        });

        // Fermer le menu si on clique en dehors
        document.addEventListener('click', function(e) {
            if (!navLinks.contains(e.target) && !burgerMenu.contains(e.target) && navLinks.classList.contains('active')) {
                toggleMenu();
            }
        });

        // Fermer le menu si on redimensionne la fenêtre au-delà de 1100px
        window.addEventListener('resize', function() {
            if (window.innerWidth > 1100 && navLinks.classList.contains('active')) {
                toggleMenu();
            }
        });
    }

    // Gestion des sous-menus en responsive
    function setupResponsiveMenu() {
        const menuItemsWithChildren = document.querySelectorAll('.nav-item, .nav-dropdown-item, .nav-sub-dropdown-item');
        menuItemsWithChildren.forEach(item => {
            const subMenu = item.querySelector('ul');
            if (subMenu) {
                if (!item.classList.contains('menu-item-has-children')) {
                    item.classList.add('menu-item-has-children');
                }
                const link = item.querySelector('a');
                let tappedOnce = false;
                link.addEventListener('click', function(e) {
                    if (window.innerWidth <= 1100) {
                        if (!item.classList.contains('open')) {
                            e.preventDefault();
                            // Fermer les autres sous-menus ouverts
                            document.querySelectorAll('.nav-item.open, .nav-dropdown-item.open, .nav-sub-dropdown-item.open').forEach(function(openItem) {
                                if (openItem !== item) openItem.classList.remove('open');
                                if (openItem.querySelector('ul')) openItem.querySelector('ul').classList.remove('open');
                            });
                            item.classList.add('open');
                            if (subMenu) subMenu.classList.add('open');
                            tappedOnce = true;
                            setTimeout(() => { tappedOnce = false; }, 600);
                        } else if (tappedOnce) {
                            // Laisse le comportement par défaut (navigation)
                            tappedOnce = false;
                        }
                    }
                });
            }
        });

        // Fonction pour ouvrir tous les parents d'un élément de menu
        function openParentMenus(element) {
            const parentItem = element.closest('.menu-item-has-children');
            if (parentItem) {
                parentItem.classList.add('open');
                // Récursivement ouvrir les parents
                const grandParent = parentItem.parentElement;
                if (grandParent) {
                    openParentMenus(grandParent);
                }
            }
        }

        // Ouvrir automatiquement les sous-menus pour l'élément actif
        const currentItems = document.querySelectorAll('.current-menu-item, .current-menu-parent, .current-menu-ancestor, .current_page_item, .current_page_parent, .current_page_ancestor');
        if (currentItems.length > 0) {
            currentItems.forEach(item => {
                openParentMenus(item);
                item.classList.add('active-menu-item');
            });
        }
    }

    // Initialiser le menu responsive
    setupResponsiveMenu();

    // Ajouter cette fonction pour réinitialiser les sous-menus lors du chargement de la page
    function checkActiveMenuItems() {
        // Vérifier si nous avons des éléments de menu actifs
        const activeItems = document.querySelectorAll('.current-menu-item, .current-menu-parent, .current-menu-ancestor, .current_page_item, .current_page_parent, .current_page_ancestor');

        if (activeItems.length > 0) {
            activeItems.forEach(item => {
                // Trouver tous les parents avec des sous-menus
                let parent = item.parentElement;
                while (parent) {
                    const menuItem = parent.closest('.menu-item-has-children');
                    if (menuItem) {
                        // Ouvrir le sous-menu
                        menuItem.classList.add('open');
                        parent = menuItem.parentElement;
                    } else {
                        break;
                    }
                }
            });
        }
    }

    // Exécuter cette fonction après le chargement complet de la page
    window.addEventListener('load', checkActiveMenuItems);

    // Réinitialiser le menu responsive lors du redimensionnement de la fenêtre
    window.addEventListener('resize', function() {
        // Réinitialiser les classes si on passe du mode responsive au mode desktop
        if (window.innerWidth > 1100) {
            document.querySelectorAll('.nav-item.open, .nav-dropdown-item.open').forEach(item => {
                item.classList.remove('open');
            });
        }
    });

    // Vérification des URLs avant redirection
    document.querySelectorAll('.language-link').forEach(link => {
        link.addEventListener('click', function(e) {
            if (this.classList.contains('no-translation')) {
                if (!confirm('This content is not available in this language. You will be redirected to the homepage. Continue?')) {
                    e.preventDefault();
                }
            }
        });
    });

    document.addEventListener('wpcf7mailsent', function(event) {
        const form = event.target;
        const formWrapper = form.closest('.wpcf7');

        // Créer le message de succès
        const successMessage = document.createElement('div');
        successMessage.className = 'form-success-message';
        successMessage.textContent = event.detail.apiResponse.message;

        // Insérer le message après le formulaire
        formWrapper.appendChild(successMessage);

        // Réinitialiser le formulaire
        form.reset();

        // Faire défiler jusqu'au message
        successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Ajouter la classe pour l'animation de disparition après 5 secondes
        setTimeout(() => {
            successMessage.classList.add('fade-out');
        }, 5000);

        // Supprimer complètement le message après l'animation
        setTimeout(() => {
            successMessage.remove();
        }, 7000);
    });

    // Supprimer la classe 'show' des sous-menus (inutile désormais)
    document.querySelectorAll('.nav-main ul ul').forEach(submenu => {
        submenu.classList.remove('show');
    });

    // Empêcher l'ouverture du sous-menu Objectives en mobile
    document.querySelectorAll('.nav-item > a[href*="objectives"], .nav-dropdown-item > a[href*="objectives"]').forEach(link => {
        link.addEventListener('click', function(e) {
            if (window.innerWidth <= 1100) {
                e.preventDefault();
                // Optionnel : naviguer directement vers la page Objectives
                window.location = this.href;
            }
        });
    });
});
document.querySelectorAll('.nav-item > a, .nav-dropdown-item > a, .nav-sub-dropdown-item > a').forEach(link => {
    const menuItem = link.closest('.nav-item, .nav-dropdown-item, .nav-sub-dropdown-item');
    if (menuItem && menuItem.classList.contains('menu-item-has-children')) {
        // Vérifie s'il n'y a pas de description
        const desc = menuItem.querySelector('.menu-item-description');
        if (!desc || !desc.textContent.trim()) {
            link.addEventListener('click', function(e) {
                e.preventDefault();
            });
            link.classList.add('no-description');
        }
    }
});

    // Filtrage automatique des catégories (version simplifiée)
    const filterCheckboxes = document.querySelectorAll('.filter-checkbox');
    
    if (filterCheckboxes.length > 0) {
        // Créer un formulaire caché pour la soumission
        let hiddenForm = document.createElement('form');
        hiddenForm.method = 'GET';
        hiddenForm.style.display = 'none';
        document.body.appendChild(hiddenForm);
        
        // Filtrage automatique lors du changement de checkbox
        filterCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function(e) {
                // Empêcher le comportement par défaut si c'est un clic direct
                if (e) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                
                // Délai pour éviter trop de requêtes
                clearTimeout(window.filterTimeout);
                window.filterTimeout = setTimeout(() => {
                    // Vider le formulaire caché
                    hiddenForm.innerHTML = '';
                    
                    // Ajouter les checkboxes cochées
                    filterCheckboxes.forEach(cb => {
                        if (cb.checked) {
                            let input = document.createElement('input');
                            input.type = 'hidden';
                            input.name = 'categories[]';
                            input.value = cb.value;
                            hiddenForm.appendChild(input);
                        }
                    });
                    
                    // Ajouter un indicateur de chargement
                    const filterContainer = document.querySelector('.filter-container');
                    if (filterContainer) {
                        filterContainer.style.opacity = '0.6';
                        filterContainer.style.pointerEvents = 'none';
                    }
                    
                    // Soumettre le formulaire
                    hiddenForm.submit();
                }, 300);
            });
        });
    }

    // Amélioration de l'UX pour les checkboxes
    const filterOptions = document.querySelectorAll('.filter-option-simple');
    filterOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            // Empêcher le comportement par défaut
            e.preventDefault();
            e.stopPropagation();
            
            // Trouver la checkbox dans cette option
            const checkbox = this.querySelector('.filter-checkbox');
            if (checkbox) {
                // Inverser l'état de la checkbox
                checkbox.checked = !checkbox.checked;
                // Déclencher l'événement change pour le filtrage
                checkbox.dispatchEvent(new Event('change'));
            }
        });
    });