{% extends "base.twig" %}

{% block content %}
  {# Fil d'Ariane (si disponible) #}
  {% if breadcrumbs %}
    <div class="breadcrumb">{{ breadcrumbs|raw }}</div>
  {% endif %}

  {# Titre de la catégorie #}
  <h1 class="category-title">{{ category_title }}</h1>

  {# Description de la catégorie (optionnelle) #}
  {% if category_description %}
    <div class="category-description">
      {{ category_description|raw }}
    </div>
  {% endif %}

  {# Section de filtrage simplifiée #}
  {% if filter_categories %}
    <section class="filter-section">
      <div class="wrapper">
        <div class="filter-container">
          <span class="filter-label">Category</span>
          {% for category in filter_categories %}
            <label class="filter-option-simple" for="filter-{{ category.id }}">
              <input type="checkbox"
                     name="categories[]"
                     value="{{ category.id }}"
                     {% if category.id in selected_categories %}checked{% endif %}
                     class="filter-checkbox"
                     id="filter-{{ category.id }}">
              <span class="filter-text">{{ category.name }} ({{ category.count }})</span>
            </label>
          {% endfor %}
        </div>
      </div>
    </section>
  {% endif %}

  {# Liste des articles de la catégorie #}
  {% if posts %}
    <section class="main-content">
      <div class="wrapper">
        <div class="posts-grid-simple">
          {% for post in posts %}
            <article class="post-card {% if post.type == 'newsletter' %}newsletter-card{% endif %}"
                     data-type="{{ post.type|default('post') }}"
                     data-categories="{% if post.categories %}{% for category in post.categories %}{{ category.term_id }}{% if not loop.last %},{% endif %}{% endfor %}{% endif %}">
              {% if post.type == 'newsletter' %}
                {# Affichage pour les newsletters #}
                <div class="post-image post-image-newsletter">
                  <div class="newsletter-icon">📧</div>
                </div>
              {% elseif post.thumbnail and post.thumbnail.src %}
                {# Affichage pour les articles avec image #}
                <div class="post-image">
                  <img src="{{ post.thumbnail.src }}" alt="{{ post.title }}" loading="lazy">
                </div>
              {% else %}
                {# Placeholder pour les articles sans image #}
                <div class="post-image post-image-placeholder">
                  <div class="placeholder-icon">📄</div>
                </div>
              {% endif %}
              
              <div class="post-content">
                <h2 class="post-title">{{ post.title }}</h2>
                
                {% if post.content and post.content|trim %}
                  <div class="post-excerpt">
                    {{ post.content|raw }}
                  </div>
                {% elseif post.type == 'newsletter' %}
                  <div class="post-excerpt newsletter-excerpt">
                    <p>Archive de newsletter - Cliquez pour consulter le contenu</p>
                  </div>
                {% endif %}
                
                <div class="post-meta">
                  {% if post.date %}
                    <span class="post-date">{{ post.date }}</span>
                  {% endif %}
                  {% if post.type == 'newsletter' %}
                    <span class="post-category">
                      <span class="category-tag newsletter-tag">Newsletter</span>
                    </span>
                  {% elseif post.categories %}
                    <span class="post-category">
                      {% for category in post.categories %}
                        <span class="category-tag">{{ category.name }}</span>
                      {% endfor %}
                    </span>
                  {% endif %}
                </div>

                <a href="{{ post.link }}" class="read-more-link"{% if post.type == 'newsletter' %} target="_blank" rel="noopener noreferrer"{% endif %}>
                  {% if current_language == 'fr' %}
                    En savoir plus
                  {% elseif current_language == 'en' %}
                    Read more
                  {% elseif current_language == 'es' %}
                    Leer más
                  {% elseif current_language == 'pt' %}
                    Leia mais
                  {% endif %}
                </a>
              </div>
            </article>
          {% endfor %}
        </div>

        {# Pagination (si disponible) #}
        {% include 'partial/pagination.twig' with { pagination: posts.pagination } %}
      </div>
    </section>
  {% else %}
    <section class="main-content">
      <div class="wrapper">
        <p class="no-posts-message">
          {% if current_language == 'fr' %}
            Aucun article trouvé dans cette catégorie.
          {% elseif current_language == 'en' %}
            No posts found in this category.
          {% elseif current_language == 'es' %}
            No se encontraron artículos en esta categoría.
          {% elseif current_language == 'pt' %}
            Nenhum artigo encontrado nesta categoria.
          {% endif %}
        </p>
      </div>
    </section>
  {% endif %}
{% endblock %}
