/*
 * Theme Name: Theme <PERSON><PERSON>
 * Description: Theme pour le site Almasi
 * Author: <PERSON><PERSON><PERSON><PERSON>
*/

/* Reset et styles de base */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    height: 100%;
    scroll-behavior: smooth;
}

body {
    font-family: '<PERSON>', sans-serif;
    font-size: 13px;
    line-height: 1.5;
    color: #333;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    position: relative;
}

p {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
}

strong {
    font-weight: 600;
    color: #385CA9;
    font-size: 1.05em;
}

h1, h2, h3, h4, h5, h6 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
}

h1 {
    font-size: 2.5rem;
}

ul, ol {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
}

li {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

img {
    margin-bottom: 1.5rem;
}

.content-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.article-content {
    padding: 2.5rem;
    margin-bottom: 0;
}

.consortium-paragraphe p {
    text-align: left;
    margin-left: 0;
    margin-bottom: 0;
}

/* Styles pour la colonne de droite */
.wp-block-column:last-child {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.wp-block-column:last-child p:first-child {
    font-size: 1.2rem;
    color: #385CA9;
    font-weight: 500;
    margin-bottom: 1rem;
}

/* Styles pour les liens réseaux */
.wp-block-social-links {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    list-style: none !important;
    padding: 0 !important;
    margin: 1rem 0 !important;
}

.wp-block-social-links li {
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
}

.wp-block-social-links li::before {
    display: none !important;
}

.wp-block-social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #385CA9;
    transition: all 0.3s ease;
}

.wp-block-social-link:hover {
    background-color: #F9A01B;
    transform: translateY(-3px);
}

.wp-block-social-link svg {
    width: 20px;
    height: 20px;
    fill: white;
}

/* Styles pour la section des articles */
.articles-section {
    margin-top: 2rem;
    padding: 2rem;
    background-color: #f8f9fa;
    border-radius: 12px;
}

.section-title {
    font-size: 1.8rem;
    color: #385CA9;
    margin-bottom: 1.5rem;
    text-align: left;
    font-weight: 600;
}

.posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.tease {
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.tease:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.tease a {
    text-decoration: none;
    color: inherit;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.tease img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-bottom: 1px solid #e9ecef;
}

.article-h2 {
    text-align: left;
    font-size: 1.3rem;
    color: #385CA9;
    width: 100%;
    margin: 0 0 1rem 0;
    padding: 0;
    line-height: 1.4;
    font-weight: 600;
    box-sizing: border-box;
}

.actualite-excerpt {
    padding: 0 1.5rem 1.5rem;
    color: #666;
    font-size: 0.95rem;
    line-height: 1.6;
    flex-grow: 1;
}

/* Styles pour les séparateurs */
.wp-block-separator {
    border: none;
    border-top: 2px solid #e9ecef;
    margin: 2rem 0;
}

/* Media queries pour la responsivité */
@media (max-width: 992px) {
    .content-wrapper {
        padding: 1.5rem;
    }
    
    .article-content {
        padding: 2rem;
    }
}

@media (max-width: 768px) {
    .content-wrapper {
        padding: 1rem;
    }
    
    .article-content {
        padding: 1.5rem;
    }
    
    .consortium-paragraphe {
        font-size: 1rem;
    }
    
    .posts-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .articles-section {
        margin-top: 1.5rem;
        padding: 1rem;
    }

    .section-title {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .wp-block-social-link {
        width: 35px;
        height: 35px;
    }

    .wp-block-social-link svg {
        width: 18px;
        height: 18px;
    }
}

/* Styles pour les articles */
.post-date {
    font-size: 1.2rem;
    color: #555;
    margin-bottom: 20px;
    font-style: italic;
}

.article-body {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.article-text {
    margin-bottom: 20px;
}

.article-image img {
    max-width: 100%;
    height: auto;
}

/* Navigation entre articles */
.post-navigation {
    margin: 40px 0;
    padding: 20px 0;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
}

.nav-previous,
.nav-next {
    flex: 0 0 48%;
    padding: 15px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.nav-previous {
    text-align: left;
}

.nav-next {
    text-align: right;
    margin-left: auto;
}

.nav-subtitle {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 5px;
}

.nav-title {
    font-weight: bold;
    color: #385CA9;
}

.nav-previous:hover,
.nav-next:hover {
    background: #f8f8f8;
    border-color: #385CA9;
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-dropdown-item {
    width: auto;
    position: relative;
    background-color: #fff;
    white-space: nowrap;
}

.nav-dropdown-item a {
    display: block;
    padding: 10px 20px;
    color: #385CA9;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
    box-sizing: border-box;
    white-space: nowrap;
}

.nav-dropdown-item a:hover {
    background-color: #F5F5F5;
    color: #F9A01B;
}

.nav-dropdown {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 5px 0;
    min-width: 200px;
    width: auto;
    white-space: nowrap;
}

/* Harmoniser tous les sous-menus */
.nav-main ul ul,
.active-parent-submenu {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    width: auto;
}
.nav-main ul ul a,
.active-parent-submenu a {
    color: #385CA9;
}

/* Responsive */
@media (max-width: 768px) {
    .post-navigation {
        flex-direction: column;
        gap: 20px;
    }

    .nav-previous,
    .nav-next {
        flex: 1;
        width: 100%;
        text-align: center;
    }
}

/* Barre de navigation */
.header {
    position: sticky;
    top: 0;
    background: #385CA9;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 100%;
}

.wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-main {
    flex: 1 1 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    padding: 10px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    flex-wrap: nowrap;
}

.hdr-logo-link {
    display: flex;
    align-items: center;
    justify-content: center;
}

.hdr-logo-img {
    max-width: 450px;
    width: 100%;
    height: auto;
    margin: 10px;
    object-fit: contain;
}

.nav-main ul {
    list-style: none;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin: 0;
    padding: 0;
}

.nav-main a {
    color: #385CA9;
    text-decoration: none;
    font-size: 1rem;
    font-weight: 800;
    transition: color 0.3s ease, transform 0.3s ease;
    padding: 5px;
    white-space: nowrap;
}

.nav-main a:hover,
.nav-item.current-menu-item > .nav-link,
.nav-item.current-menu-parent > .nav-link {
    color: #F9A01B;
}

/* Sous-menus */
.nav-main ul ul {
    display: none;
    position: absolute;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 10px 0;
    opacity: 0;
    visibility: hidden;
    transition: transform 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
    transform: translateY(10px);
    z-index: 1000;
    width: auto;
    white-space: nowrap;
}

.nav-main ul ul ul {
    top: 0;
    left: 100%;
    transform: translateX(10px);
    width: auto;
    white-space: nowrap;
}

/* Affichage des sous-menus au survol */
.nav-main li:hover > ul {
    display: block;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Correction pour les sous-sous-menus */
.nav-main ul ul li:hover > ul {
    display: block;
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

/* Masquer les sous-sous-menus en mode responsive */
@media (max-width: 768px) {
    .nav-main ul ul ul {
        display: none !important;
    }
}

.nav-main ul ul a,
.language-switcher ul a {
    padding: 10px 20px;
    display: block;
    white-space: nowrap;
    color: #385CA9;
}

.nav-main ul ul a:hover,
.nav-main .current-menu-item ul a:hover {
    background: #F5F5F5 !important;
    color: #F9A01B !important;
}

/* Burger menu */
.burger-menu {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: transparent;
    border: none;
    cursor: pointer;
    z-index: 1001;
    margin-right: 15px;
    position: relative;
}

.burger-bar {
    display: block;
    width: 25px;
    height: 3px;
    margin: 3px 0;
    background-color: #F9A01B;
    border-radius: 3px;
    transition: all 0.3s ease;
    transform-origin: center;
}

/* Animation du burger menu */
.burger-menu.active .burger-bar:nth-child(1) {
    transform: translateY(9px) rotate(45deg);
}

.burger-menu.active .burger-bar:nth-child(2) {
    opacity: 0;
    transform: scale(0);
}

.burger-menu.active .burger-bar:nth-child(3) {
    transform: translateY(-9px) rotate(-45deg);
}

/* Responsive */
@media (max-width: 1100px) {
    .burger-menu {
        display: flex;
    }

    .nav-links {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: #fff;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        padding: 15px;
        max-height: 80vh;
        overflow-y: auto;
        border-radius: 0;
    }

    .nav-links.active {
        display: flex;
        animation: slideDown 0.3s ease-in-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .nav-main ul {
        flex-direction: column;
        gap: 10px;
    }

    .nav-main ul ul {
        position: static;
        display: none;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        width: 100%;
        padding: 0;
        margin-top: 5px;
        margin-bottom: 5px;
        background-color: #f8f8f8;
        border-radius: 5px;
        overflow: hidden;
        transition: max-height 0.3s ease-in-out;
    }

    .nav-main ul ul.show {
        display: block;
        max-height: 1000px;
    }

    .nav-main ul ul a {
        padding: 10px 20px 10px 30px;
        font-size: 0.95rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .nav-main ul ul a:last-child {
        border-bottom: none;
    }

    /* Masquer le sous-menu d'Objectives dans le menu burger (responsive) */
    .nav-item > a[href*="objectives"] + ul,
    .nav-dropdown-item > a[href*="objectives"] + ul {
        display: none !important;
    }

    /* Cacher la flèche et la bordure pour Objectives en responsive */
    .nav-item > a[href*="objectives"],
    .nav-dropdown-item > a[href*="objectives"] {
        /* Masquer la flèche ▼ ajoutée par ::after */
        position: relative;
    }
    .nav-item > a[href*="objectives"]::after,
    .nav-dropdown-item > a[href*="objectives"]::after {
        display: none !important;
        content: none !important;
    }
    /* Retirer la bordure du bas */
    .nav-main ul ul a[href*="objectives"] {
        border-bottom: none !important;
    }
}

@media (max-width: 1100px) {
    .nav-dropdown.active-parent-submenu,
    .active-parent-submenu {
        background: #fff !important;
        border-radius: 8px !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        width: auto !important;
    }
}

@media (max-width: 1100px) {
    .nav-main ul ul {
        display: none;
    }
    .nav-main ul ul.open {
        display: block !important;
        position: static;
        opacity: 1;
        visibility: visible;
        max-height: 1000px;
        background: #f8f8f8;
        box-shadow: none;
        border-radius: 5px;
        width: 100%;
        padding: 0;
        margin-top: 5px;
        margin-bottom: 5px;
        overflow: hidden;
    }
    .nav-main ul ul ul {
        display: none !important;
    }
}

/* Burger menu */
.burger-menu {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: transparent;
    border: none;
    cursor: pointer;
    z-index: 1001;
    margin-right: 15px;
    position: relative;
    order: -1;
}

.burger-bar {
    display: block;
    width: 25px;
    height: 3px;
    margin: 3px 0;
    background-color: #F9A01B;
    border-radius: 3px;
    transition: all 0.3s ease;
    transform-origin: center;
}

/* Animation du burger menu */
.burger-menu.active .burger-bar:nth-child(1) {
    transform: translateY(9px) rotate(45deg);
}

.burger-menu.active .burger-bar:nth-child(2) {
    opacity: 0;
    transform: scale(0);
}

.burger-menu.active .burger-bar:nth-child(3) {
    transform: translateY(-9px) rotate(-45deg);
}

/* Conteneur des liens de navigation */
.nav-links {
    display: flex;
    align-items: center;
    position: relative;
    flex: 1;
}

/* Affichage des sous-menus au survol */
.nav-main li:hover > ul {
    display: block;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Sélecteur de langue */
.language-switcher {
    position: relative;
    margin-left: 40px;
    z-index: 100;
}

/* Styles pour le sélecteur de langue */
.language-switcher {
    position: relative;
}

.language-switcher-toggle {
    background-color: #fdfdfd;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 1rem;
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.language-switcher-toggle:hover {
    background-color: #f5f5f5;
    border-color: #0073e6;
}

.language-switcher-toggle .arrow {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

/* Dropdown langue caché par défaut */
.language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    list-style: none;
    margin: 0;
    padding: 8px 0;
    z-index: 100;
    display: none;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

.language-switcher.open .language-dropdown {
    display: block !important;
    margin-top: 5px;
    visibility: visible;
    opacity: 1;
}

.language-item {
    padding: 8px 16px;
    transition: background-color 0.3s ease;
}

.language-item:hover {
    background-color: #f5f5f5;
}

.language-item.current {
    font-weight: bold;
    color: #0073e6;
}

.language-switcher-toggle:disabled {
    color: gray;
    pointer-events: none;
    cursor: default;
}

/* Animations */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

.nav-main li:hover > ul {
    animation: slideDown 0.3s ease forwards;
}

.nav-main ul ul {
    animation: slideUp 0.3s ease forwards;
}

/* Hero Section styles */
.hero-section {
    position: relative;
    overflow: hidden;
    display: flex;
    justify-content: center;
}

.hero-banner {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background-size: cover;
    background-position: center;
    height: 28vh;
    min-height: 15vh;
    width: 100%;
    padding: 1rem;
}

.hero-content {
    text-align: center;
    padding: 0.8rem;
    position: relative;
    z-index: 2;
    max-width: 90%;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(4px);
    width: 1200px;
    word-wrap: break-word;
}

.hero-titles {
    color: white;
    text-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    margin-bottom: 2rem;
    padding: 0 1rem;
}

.hero-main-title {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 800;
    margin-bottom: 1rem;
    line-height: 1.2;
    letter-spacing: -0.5px;
    color: #FFF;
    word-break: break-word;
}

.hero-subtitle {
    font-size: clamp(1rem, 2vw, 1.5rem);
    font-weight: 400;
    max-width: 80%;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin: 0 auto;
    word-break: break-word;
}


/* Fil d'ariane */
.breadcrumb {
    margin: 0 20px;
    margin-bottom: 20px;
    padding: 10px 15px;
    font-size: 16px;
    color: #666;
    background-color: #f9f9f9;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
    line-height: 1.5;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
}

.breadcrumb a {
    color: #0073aa;
    text-decoration: none;
    transition: color 0.3s ease;
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.breadcrumb a:hover {
    color: #005f8d;
    text-decoration: underline;
}

.breadcrumb > span {
    color: #555;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.breadcrumb-separator {
    margin: 0 5px;
    color: #999;
}

.category-description {
    padding: 20px;
}

.posts-grid article {
    flex: 1 1 calc(33.333% - 1.2rem);
    box-sizing: border-box;
    padding: 1.5rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
}

.posts-grid article img {
    align-self: center;
    max-width: 35%;
    height: auto;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.posts-grid article a {
    text-decoration: none;
    color: #000;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.posts-grid article:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.article-h2 a {
    color: #385CA9;
    text-decoration: none;
    transition: color 0.3s ease;
}

.article-h2 a:hover {
    color: #F9A01B;
}

.tease {
    font-size: 1rem;
    margin-bottom: 1rem;
}

/* Styles pour les articles sans image */
.posts-grid article.no-image {
    padding-top: 2rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.posts-grid article.no-image p {
    margin-top: 0;
}

.article-image {
    flex-shrink: 0;
}

.article-image img {
    margin: 20px;
    max-width: 250px;
    height: auto;
}

.article-text {
    flex-grow: 1;
}

/* Page Consortium */
.organisations-grid {
    padding: 2rem;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
}

.grid-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    width: 100%;
    max-width: 1200px;
}

.organisation-card {
    background: #ffffff;
    border: 1px solid #eaeaea;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s ease;
    height: 100%;
}

.organisation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    border-color: #385CA9;
}

.organisation-card a {
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
}

.organisation-logo {
    width: 100%;
    max-width: 200px;
    height: 130px;
    object-fit: contain;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.organisation-card:hover .organisation-logo {
    transform: scale(1.05);
}

.organisation-title {
    font-size: 1.1rem;
    text-align: center;
    color: #333;
    margin: 0;
    line-height: 1.4;
    font-weight: 600;
}

@media (max-width: 992px) {
    .organisations-grid {
        padding: 1.5rem;
    }

    .grid-container {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .organisation-logo {
        height: 110px;
    }
}

@media (max-width: 768px) {
    .organisations-grid {
        padding: 1rem;
    }

    .grid-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .organisation-logo {
        height: 100px;
    }
}

@media (max-width: 480px) {
    .grid-container {
        grid-template-columns: 1fr;
    }

    .organisation-card {
        padding: 1rem;
    }

    .organisation-title {
        font-size: 1rem;
    }
}

/* Page organisation */
.organisation-wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px;
    width: 100%;
    box-sizing: border-box;
}

.organisation {
    background: white;
    border-radius: 12px;
    padding: 20px;
    max-width: 1200px;
    width: 100%;
    text-align: left;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: auto;
    box-sizing: border-box;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.organisation img {
    width: 300px;
    max-width: 100%;
    height: 150px;
    object-fit: contain;
    display: block;
    border-radius: 8px;
    margin: 0 auto 15px;
    padding-bottom: 8px;
}

.organisation h1 {
    font-size: 24px;
    color: #333;
    margin: 10px 0;
}

.organisation-description {
    font-size: 16px;
    color: #666;
    line-height: 1.7;
    margin-bottom: 15px;
    padding: 10px 0;
    white-space: pre-line;
}

.organisation-link {
    display: block;
    padding: 10px 20px;
    background-color: #0073aa;
    color: white;
    text-decoration: none;
    font-size: 16px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
    text-align: center;
    margin-left: 25%;
    margin-right: 25%;
}

.organisation-link:hover {
    background-color: #005f87;
}

/* Page actualités et objectifs */
.actualites-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 20px;
    justify-content: center;
    box-sizing: border-box;
}

.actualites-grid .actualite-item {
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.actualites-grid .actualite-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.actualites-grid .actualite-item .actualite-image {
    width: 100%;
    height: 200px;
    object-fit: contain;
    background-color: #f8f8f8;
    border-bottom: 1px solid rgba(56, 92, 169, 0.1);
    padding: 10px;
}

.actualites-grid .actualite-item .actualite-title {
    font-size: 1.2em;
    color: #1D2027;
    font-weight: 600;
    line-height: 1.4;
    margin: 20px;
    flex-grow: 1;
    word-wrap: break-word;
    overflow-wrap: break-word;
    width: auto;
    max-width: 100%;
    box-sizing: border-box;
}

.read-more-button {
    margin: 0 20px 20px;
    padding: 10px 20px;
    background-color: #385CA9;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    text-align: center;
    font-weight: 500;
    transition: background-color 0.3s ease;
    align-self: flex-start;
}

.read-more-button:hover {
    background-color: #2c4a8a;
    color: white;
}

@media (max-width: 1200px) {
    .actualites-grid {
        max-width: 100%;
        padding: 20px;
    }
}

@media (max-width: 768px) {
    .actualites-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        justify-content: start;
        padding: 15px;
    }

    .actualites-grid .actualite-item .actualite-image {
        height: 180px;
    }

    .actualites-grid .actualite-item .actualite-title {
        font-size: 1.1em;
        margin: 15px;
        hyphens: auto;
    }

    .read-more-button {
        margin: 0 15px 15px;
        padding: 8px 16px;
        font-size: 0.9em;
        width: auto;
        display: inline-block;
    }
}

@media (max-width: 480px) {
    .actualites-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 10px;
    }

    .actualites-grid .actualite-item .actualite-image {
        height: 160px;
    }

    .actualites-grid .actualite-item .actualite-title {
        font-size: 1em;
        margin: 12px;
        hyphens: auto;
    }

    .read-more-button {
        margin: 0 12px 12px;
        padding: 8px 14px;
        font-size: 0.85em;
        width: auto;
        display: inline-block;
        text-align: center;
    }

    .wrapper {
        padding: 0 10px;
    }
}

/* Page Media Kit */
.wp-block-file {
    background: #f8f8f8;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.wp-block-file__embed {
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.wp-block-file a:not(.wp-block-file__button) {
    color: #385CA9;
    text-decoration: none;
    margin-right: 20px;
    font-weight: 600;
    font-size: 1.1em;
}

.wp-block-file__button {
    background-color: #385CA9;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
    font-size: 1.3em;
    transition: background-color 0.3s ease;
}

.wp-block-file__button:hover {
    background-color: #2c4a8a;
}

/* Image block styles */
.wp-block-image {
    margin: 30px 0;
    text-align: center;
}

.wp-block-image img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
}

.wp-block-image figcaption {
    color: #666;
    font-size: 0.9rem;
    margin-top: 10px;
    text-align: center;
}

.wp-block-image figcaption a {
    color: white;
    text-decoration: none !important;
}

.wp-element-caption {
    display: inline-block;
    padding: 10px 20px;
    background-color: #385CA9;
    color: white;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.wp-element-caption a {
    color: white;
    text-decoration: none;
}

.wp-element-caption:hover {
    background-color: #2c4a8a;
}

/* Responsive */
@media (max-width: 768px) {
    .wp-block-file {
        padding: 15px;
    }

    .wp-block-file a:not(.wp-block-file__button) {
        font-size: 1em;
    }

    .wp-block-file__button {
        display: block;
        text-align: center;
        margin-top: 10px;
        font-size: 1em;
    }

    .wp-block-image {
        margin: 20px 0;
    }

    .wp-element-caption {
        font-size: 0.8em;
        padding: 6px 12px;
    }
}

@media (max-width: 480px) {
    .wp-block-file {
        padding: 10px;
    }

    .wp-block-file a:not(.wp-block-file__button) {
        font-size: 0.9em;
    }

    .wp-block-file__button {
        font-size: 0.9em;
        padding: 6px 12px;
    }

    .wp-element-caption {
        font-size: 0.7em;
        padding: 4px 8px;
    }
}

/* Page Contact */
.wp-block-contact-form-7-contact-form-selector {
    width: 1200px;
}

.article-text .wpcf7-form {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.article-text .wpcf7-form p {
    margin-bottom: 20px;
}

.article-text .wpcf7-form label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.article-text .wpcf7-form .wpcf7-form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    color: #333;
    box-sizing: border-box;
}

.article-text .wpcf7-form .wpcf7-form-control:focus {
    border-color: #385CA9;
    outline: none;
}

.article-text .wpcf7-form .wpcf7-submit {
    background-color: #385CA9;
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.article-text .wpcf7-form .wpcf7-submit:hover {
    background-color: #2c4a8a;
}

.article-text .wpcf7-form .wpcf7-spinner {
    display: none;
}

.article-text .wpcf7-form .wpcf7-response-output {
    margin-top: 20px;
    padding: 10px;
    border-radius: 4px;
    display: none;
}

.article-text .wpcf7-form .wpcf7-response-output.wpcf7-mail-sent-ok {
    background-color: #dff0d8;
    color: #3c763d;
}

.article-text .wpcf7-form .wpcf7-response-output.wpcf7-mail-sent-ng {
    background-color: #f2dede;
    color: #a94442;
}

/* Message de succès */
.form-success-message {
    display: block !important;
    border: none !important;
    background-color: #4CAF50 !important;
    color: white !important;
    padding: 15px 20px !important;
    border-radius: 8px !important;
    font-size: 16px !important;
    text-align: center !important;
    margin: 20px 0 !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
    animation: slideInUp 0.5s ease-in-out;
    position: relative;
    width: 100% !important;
    box-sizing: border-box !important;
    font-weight: 500 !important;
    line-height: 1.5 !important;
}

/* Animation pour le message */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animation de disparition */
.form-success-message.fade-out {
    animation: fadeOut 0.5s ease-in-out forwards;
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* Contenu page */
summary {
    font-size: 17px;
}

/* Footer */
.site-footer {
    background-color: #385CA9;
    color: white;
    padding: 40px 20px;
    text-align: center;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    max-width: 1200px;
    margin: 0 auto;
    gap: 20px;
}

.footer-menu,
.footer-social,
.footer-language {
    margin: 10px;
    flex: 1;
    min-width: 200px;
    text-align: left;
}

.footer-menu ul,
.footer-social ul,
.footer-language ul {
    list-style: none;
    padding: 0;
}

.footer-menu li,
.footer-social li,
.footer-language li {
    margin: 5px 0;
}

.footer-menu a,
.footer-social a,
.footer-language a {
    text-decoration: none;
    color: #ecf0f1;
    transition: color 0.3s;
    font-weight: 400;
}

.footer-menu a:hover,
.footer-social a:hover,
.footer-language a:hover {
    color: #f1c40f;
}

.footer-language .active-language {
    font-weight: bold;
    text-decoration: underline;
}


.footer-eu-funding {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: stretch;
    gap: 60px;
    margin: 30px auto 20px;
    max-width: 1200px;
    width: 100%;
}


.footer-eu-funding-block {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1 1;
    min-width: 0;
    max-width: 480px;
    gap: 18px;
    box-sizing: border-box;
}

.footer-eu-flag {
    max-width: 260px;
    width: 100%;
    height: auto;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 10px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
    margin-bottom: 0;
}

.footer-era-img {
    max-width: 350px;
    width: 100%;
    height: auto;
    background: none;
    padding: 0;
    border-radius: 0;
    box-shadow: none;
    flex-shrink: 0;
    margin-bottom: 0;
}

.footer-eu-text,
.footer-era-text {
    margin: 0;
    padding-left: 30px;
    text-align: left;
    font-size: 1.15em;
    line-height: 1.5;
}

@media (max-width: 700px) {
    .footer-eu-funding {
        flex-direction: column;
        gap: 30px;
        align-items: center;
    }
    .footer-eu-funding-block {
        max-width: 100%;
    }
}

.footer-copyright {
    margin-top: 20px;
    font-size: 16px;
}

/* Page 404 */
.error-404-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 50px 20px;
}

.error-404-content {
    max-width: 600px;
    text-align: center;
}

.error-404-title {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #385CA9;
}

.error-404-image {
    margin: 30px 0;
}

.error-404-image img {
    max-width: 100%;
    height: auto;
}

.error-404-message {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.error-404-button {
    display: inline-block;
    padding: 12px 25px;
    background-color: #385CA9;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.error-404-button:hover {
    background-color: #F9A01B;
}

@media (max-width: 576px) {
    .error-404-title {
        font-size: 1.8rem;
    }

    .error-404-message {
        font-size: 1rem;
    }

    .error-404-button {
        padding: 10px 20px;
    }
}

/* Responsive */
@media (max-width: 1100px) {
    .wrapper {
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
        gap: 0;
        position: relative;
        min-height: 70px;
    }

    .hdr-logo-link {
        margin-right: 0;
        flex: 1;
        text-align: center;
    }

    .hdr-logo-img {
        max-width: 300px;
    }

    .burger-menu {
        display: flex;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
    }

    /* Repositionnement du sélecteur de langue */
    .language-switcher {
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        margin-left: 0;
    }

    .nav-main {
        order: 3;
        width: 100%;
        margin-top: 10px;
        padding: 0;
        background-color: transparent;
        box-shadow: none;
        border-radius: 0;
    }

    .nav-links {
        order: 2;
        width: 100%;
        top: 100%;
        left: 0;
        margin-top: 0;
        border-radius: 0;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }

    /* Styles pour le menu principal en responsive */
    .nav-menu {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        width: 100%;
    }

    .nav-links {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: #fff;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        padding: 15px;
        max-height: 80vh;
        overflow-y: auto;
        border-radius: 0;
        margin-top: 0;
    }

    .nav-links.active {
        display: flex;
        animation: slideDown 0.3s ease-in-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Styles pour les sous-menus en responsive */
    .nav-main ul ul {
        position: static;
        display: none;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        width: 100%;
        padding: 0;
        margin-top: 5px;
        margin-bottom: 5px;
        background-color: #f8f8f8;
        border-radius: 5px;
        overflow: hidden;
        transition: max-height 0.3s ease-in-out;
    }

    .nav-main ul ul.show {
        display: block;
        max-height: 1000px;
    }

    .nav-main ul ul a {
        padding: 10px 20px 10px 30px;
        font-size: 0.95rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .nav-main ul ul a:last-child {
        border-bottom: none;
    }

    /* Masquer les sous-sous-menus en mode responsive */
    @media (max-width: 768px) {
        .nav-main ul ul ul {
            display: none !important;
        }
    }

    @media (max-width: 768px) {
        .nav-main ul ul ul {
            display: none !important;
        }
    }

    .nav-dropdown-item.menu-item-has-children > a::after {
        content: "...";
        font-size: 0.9rem;
        margin-left: 8px;
        color: #F9A01B;
    }

    .nav-dropdown-item.menu-item-has-children > a {
        position: relative;
    }

    /* Styles pour les items de menu avec sous-menus */
    .nav-item {
        width: 100%;
        position: relative;
    }

    .nav-item > a {
        padding: 12px 15px !important;
        border-radius: 6px;
        transition: background-color 0.3s ease, color 0.3s ease;
        display: block;
        width: 100%;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .nav-item > a:hover {
        background-color: rgba(56, 92, 169, 0.1);
    }

    .nav-item.menu-item-has-children > a {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .nav-item.menu-item-has-children > a::after {
        content: "▼";
        font-size: 0.7rem;
        margin-left: 8px;
        transition: transform 0.3s ease;
        color: #385CA9;
    }

    .nav-item.menu-item-has-children.open > a {
        background-color: rgba(56, 92, 169, 0.1);
        border-radius: 6px 6px 0 0;
    }

    .nav-item.menu-item-has-children.open > a::after {
        transform: rotate(180deg);
        color: #F9A01B;
    }

    .nav-dropdown-item {
        width: 100%;
        position: relative;
    }

    .nav-dropdown-item > a {
        display: block;
        padding: 10px 15px;
        transition: background-color 0.3s ease, color 0.3s ease;
    }

    .nav-dropdown-item.menu-item-has-children > a {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .nav-dropdown-item.menu-item-has-children > a::after {
        content: "▼";
        font-size: 0.7rem;
        margin-left: 8px;
        transition: transform 0.3s ease;
        color: #385CA9;
    }

    .nav-dropdown-item.menu-item-has-children.open > a {
        background-color: rgba(56, 92, 169, 0.05);
        font-weight: 600;
    }

    .nav-dropdown-item.menu-item-has-children.open > a::after {
        transform: rotate(180deg);
        color: #F9A01B;
    }

    /* Styles pour les sous-sous-menus */
    .nav-sub-dropdown-item > a {
        padding-left: 45px !important;
        font-size: 0.9rem;
    }

    .nav-sub-dropdown-item.menu-item-has-children > a::after {
        content: "▼";
        font-size: 0.7rem;
        margin-left: 8px;
        transition: transform 0.3s ease;
        color: #385CA9;
    }

    .nav-sub-dropdown-item.menu-item-has-children.open > a {
        background-color: rgba(249, 160, 27, 0.05);
        font-weight: 600;
    }

    .nav-sub-dropdown-item.menu-item-has-children.open > a::after {
        transform: rotate(180deg);
        color: #F9A01B;
    }

    .nav-main li:hover > ul {
        animation: none;
        display: none;
    }

    .nav-main ul ul {
        animation: none;
    }

    .language-switcher {
        margin-left: 10px;
        order: 0;
    }

    .hero-banner {
        background: none !important;
    }
}

@media (max-width: 992px) {
    .hero-banner {
        height: 35vh;
        min-height: 200px;
        background-attachment: scroll;
    }

    .hero-content {
        margin-left: 5%;
        width: 90%;
        padding: 1.5rem;
        transform: none;
    }

    .hero-main-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    .form-success-message {
        font-size: 14px !important;
        padding: 12px 15px !important;
        margin: 15px 0 !important;
    }
}

@media (max-width: 576px) {
    .hero-banner {
        height: 40vh;
        min-height: auto;
    }

    .hero-content {
        margin: 0 auto;
        width: 95%;
        text-align: center;
    }

    .hero-main-title {
        font-size: 1.75rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    body {
        font-size: 0.875rem;
    }

    .header {
        padding: 3px 0;
    }

    #content {
        padding: 0 15px;
    }

    .posts-grid article {
        flex: 1 1 100%;
    }

    #footer {
        padding: 10px;
    }

    .hdr-logo-img {
        max-width: 180px;
        margin: 3px;
    }

    .wrapper {
        min-height: 50px;
    }

    .burger-menu {
        width: 35px;
        height: 35px;
        left: 5px;
    }

    .burger-bar {
        width: 22px;
        height: 2px;
    }

    .language-switcher {
        right: 5px;
    }

    .language-switcher-toggle {
        padding: 5px 8px;
        font-size: 0.85rem;
    }

    .language-dropdown {
        right: -5px;
    }

    .nav-main {
        margin-top: 5px;
    }

    .nav-links {
        padding: 10px;
    }

    .nav-item > a {
        font-size: 1rem;
        padding: 10px !important;
    }
}

@media (max-width: 1200px) {
    .content-wrapper {
        width: 100%;
        max-width: 95%;
        padding: 20px 15px;
    }

    .hero-content {
        max-width: 95%;
    }

    .wp-block-contact-form-7-contact-form-selector {
        width: 100%;
    }
}

@media (max-width: 992px) {
    .posts-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .article-body {
        flex-direction: column-reverse;
    }

    .article-image {
        margin: 0 auto 20px;
        text-align: center;
    }

    .article-image img {
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.7rem;
    }

    p {
        font-size: 1rem;
    }

    .posts-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .organisation-link {
        margin-left: 10%;
        margin-right: 10%;
    }

    .hdr-logo-img {
        max-width: 220px;
        margin: 5px;
    }

    .burger-menu {
        left: 10px;
    }

    .language-switcher {
        right: 10px;
    }

    .language-switcher-toggle {
        padding: 6px 10px;
        font-size: 0.9rem;
    }

    .header {
        padding: 5px 0;
    }

    .wrapper {
        padding: 0 10px;
        min-height: 60px;
    }

    .breadcrumb {
        margin: 0 15px 15px;
        padding: 8px 12px;
        font-size: 15px;
        gap: 3px;
    }

    .breadcrumb a,
    .breadcrumb > span {
        max-width: 200px;
    }

    .footer-content {
        justify-content: center;
        text-align: center;
    }

    .footer-menu,
    .footer-social,
    .footer-language {
        min-width: 250px;
        margin: 15px 10px;
        text-align: center;
    }

    .footer-eu-funding {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .footer-eu-funding img {
        margin-bottom: 15px;
        order: 1;
    }

    .footer-eu-funding p {
        order: 2;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .breadcrumb {
        font-size: 13px;
        padding: 8px 10px;
        margin: 0 10px 15px;
        line-height: 1.4;
        gap: 2px;
    }

    .breadcrumb a,
    .breadcrumb > span {
        max-width: 150px;
    }

    .breadcrumb-separator {
        margin: 0 3px;
    }

    .organisation-link {
        margin-left: 0;
        margin-right: 0;
    }

    .organisation {
        padding: 15px;
    }

    .organisation h1 {
        font-size: 20px;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
    }

    .organisation img {
        width: 200px;
        height: auto;
    }

    .organisation-description {
        font-size: 14px;
    }

    .site-footer {
        padding: 30px 15px;
    }

    .footer-content {
        gap: 15px;
    }

    .footer-menu,
    .footer-social,
    .footer-language {
        min-width: 100%;
        margin: 10px 0;
    }

    .footer-eu-funding {
        margin: 20px auto 15px;
    }

    .footer-eu-funding img {
        max-width: 250px;
    }

    .footer-copyright {
        font-size: 14px;
    }

    .nav-links {
        padding: 10px;
        max-height: 70vh;
    }

    .nav-item > a {
        padding: 10px 12px !important;
        font-size: 0.95rem;
    }

    .nav-main ul ul a {
        padding: 8px 15px 8px 25px;
        font-size: 0.9rem;
    }

    .nav-main ul ul ul {
        margin-left: 5px;
        margin-right: 5px;
    }

    .nav-main ul ul ul a {
        padding-left: 35px;
        font-size: 0.85rem;
    }
}

/* Tableaux responsifs */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    overflow-x: auto;
    display: block;
    max-width: 100%;
}

table, th, td {
    border: 1px solid #ddd;
}

th, td {
    padding: 12px;
    text-align: left;
}

th {
    background-color: #385CA9;
    color: white;
}

tr:nth-child(even) {
    background-color: #f2f2f2;
}

@media (max-width: 768px) {
    table {
        font-size: 14px;
    }

    th, td {
        padding: 8px;
    }
}

@media (max-width: 576px) {
    table {
        font-size: 12px;
    }

    th, td {
        padding: 6px;
        word-break: break-word;
    }
}

@media (max-width: 1100px) {
    .current-menu-parent > ul.show,
    .current-menu-ancestor > ul.show,
    .current_page_parent > ul.show,
    .current_page_ancestor > ul.show {
        max-height: 1000px !important;
        transition: max-height 0.5s ease-in-out;
    }
}

@media (max-width: 1100px) {
    .nav-dropdown-item a {
        padding: 12px 20px 12px 30px;
        white-space: normal;
    }

    .nav-dropdown {
        box-shadow: none;
        background-color: #f8f8f8;
        border-radius: 5px;
        width: 100%;
        white-space: normal;
    }

    .nav-sub-dropdown {
        width: 100% !important;
        white-space: normal !important;
    }

    .nav-sub-dropdown-item {
        width: 100% !important;
        white-space: normal !important;
    }

    .nav-sub-dropdown-link {
        white-space: normal !important;
        padding: 12px 20px 12px 40px;
    }

    .nav-main ul ul a,
    .nav-main .current-menu-item ul a,
    .nav-main .current-menu-parent ul a,
    .nav-main .current-menu-ancestor ul a{
        white-space: normal !important;
    }
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background-color: #F9A01B;
}

.posts-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.tease {
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(56, 92, 169, 0.1);
}

.tease:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #385CA9;
}

.tease a {
    text-decoration: none;
    color: inherit;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.tease img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-bottom: 1px solid #e9ecef;
    transition: transform 0.3s ease;
}

.tease:hover img {
    transform: scale(1.05);
}

.tease:hover .article-h2 {
    color: #F9A01B;
}

.actualite-excerpt {
    padding: 0 1.5rem 1.5rem;
    color: #666;
    font-size: 0.95rem;
    line-height: 1.6;
    flex-grow: 1;
}

.view-all-button-container {
    text-align: left;
    margin-top: 1rem;
}

.view-all-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #385CA9;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 600;
    font-size: 17px;
    transition: background-color 0.3s ease;
}

.view-all-button:hover {
    background-color: #F9A01B;
}

@media (max-width: 992px) {
    .posts-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .article-content {
        padding: 2rem;
    }
}

@media (max-width: 768px) {
    .home-layout {
        gap: 1.5rem;
    }

    .posts-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .section-title {
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }

    .tease img {
        height: 180px;
    }

    .article-content {
        padding: 1.5rem;
    }

    .consortium-paragraphe {
        font-size: 1rem;
    }
}

/* Styles pour les colonnes WordPress */
.wp-block-columns {
    margin-bottom: 2rem;
}

.wp-block-column {
    text-align: left;
}

/* Styles pour le blockquote */
.wp-block-quote {
    margin: 0 0 1.5rem 0;
    padding: 0;
    border: none;
}

.wp-block-quote h2 {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
    font-weight: normal;
    margin: 0;
}

/* Styles pour les paragraphes */
.wp-block-column p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
    margin-bottom: 1.5rem;
    text-align: left;
}

/* Styles pour les listes */
.wp-block-list {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.wp-block-list li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
}

.wp-block-list li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: #385CA9;
}

.post-date {
    font-size: 0.9rem;
    color: #666;
    margin: 8px 0;
    font-style: italic;
}

.tease-post {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.tease-post:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.tease-post a {
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.tease-post img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.tease-post .article-h2 {
    font-size: 1.2rem;
    margin: 15px 15px 10px;
    color: #385CA9;
}

.tease-post .actualite-excerpt {
    padding: 0 15px 15px;
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
    flex-grow: 1;
}

/* Styles pour la section de filtrage simplifiée */
.filter-section {
    background: #f8f9fa;
    padding: 1.5rem 0;
    margin-bottom: 2rem;
    border-bottom: 1px solid #e9ecef;
}

.filter-container {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    max-width: 800px;
    margin: 0 auto;
    flex-wrap: wrap;
}

.filter-label {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
    margin-right: 1rem;
}

.filter-option-simple {
    display: flex;
    align-items: center;
    cursor: pointer;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 1rem;
    user-select: none;
    position: relative;
    /* Styles pour le label */
    margin: 0;
    text-decoration: none;
    color: inherit;
}

.filter-option-simple:hover {
    border-color: #007bff;
    background: #f8f9ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.filter-option-simple:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 123, 255, 0.1);
}

.filter-checkbox {
    width: 16px;
    height: 16px;
    accent-color: #007bff;
    margin: 0;
    cursor: pointer;
}

.filter-text {
    font-weight: 500;
    color: #333;
}



/* Grille d'articles simplifiée en 3 colonnes */
.posts-grid-simple {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
    min-height: 400px; /* Évite que la grille s'effondre quand il y a peu d'éléments */
}

/* Styles spécifiques pour les newsletters */
.newsletter-single {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.newsletter-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.newsletter-header {
    padding: 2rem;
    border-bottom: 1px solid #e9ecef;
}

.newsletter-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.newsletter-date {
    color: #666;
}

.newsletter-tag {
    background: #007bff;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.newsletter-title {
    margin: 0;
    font-size: 2rem;
    color: #333;
    line-height: 1.3;
}

.newsletter-body {
    padding: 2rem;
    line-height: 1.6;
}

.newsletter-body img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
}

.newsletter-footer {
    padding: 1.5rem 2rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.newsletter-info {
    margin: 0;
    color: #666;
    font-style: italic;
}

.back-link {
    display: inline-flex;
    align-items: center;
    color: #007bff;
    text-decoration: none;
    margin-bottom: 2rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.back-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

.post-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
}

.post-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.post-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.post-card:hover .post-image img {
    transform: scale(1.05);
}

/* Placeholder pour les articles sans image */
.post-image-placeholder {
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #dee2e6;
}

.placeholder-icon {
    font-size: 3rem;
    color: #adb5bd;
    opacity: 0.6;
}

/* Styles pour les newsletters */
.post-image-newsletter {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
}

.newsletter-icon {
    font-size: 3rem;
    color: white;
    opacity: 0.9;
}

.newsletter-card {
    border-color: #667eea;
}

.newsletter-card:hover {
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.newsletter-tag {
    background: #667eea;
    color: white;
}

/* Style pour l'extrait des newsletters */
.newsletter-excerpt {
    font-style: italic;
    color: #666;
}

.newsletter-excerpt p {
    margin: 0;
    font-size: 0.9rem;
}

/* Style spécifique pour le bouton Read more des newsletters */
.newsletter-card .read-more-link {
    background: #667eea;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

.newsletter-card .read-more-link:hover {
    background: #5a67d8;
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

/* Fallback pour les articles sans image */
.post-card:not(:has(.post-image)) .post-content {
    border-top: none;
}

.post-content {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.post-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.post-excerpt {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    flex-grow: 1;
}

.post-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    color: #888;
}

.post-date {
    font-weight: 500;
}

.post-category {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.category-tag {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.read-more-link {
    background: #007bff;
    color: white;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    margin-top: auto;
    display: inline-block;
    width: fit-content;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.read-more-link:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* Message quand aucun article n'est trouvé */
.no-posts-message {
    text-align: center;
    font-size: 1.2rem;
    color: #666;
    padding: 3rem 0;
}

/* Style pour éviter que la grille s'effondre avec peu d'éléments */
.posts-grid-simple:has(.post-card:only-child) {
    grid-template-columns: minmax(300px, 600px);
    justify-content: center;
}

@media (max-width: 768px) {
    .posts-grid-simple:has(.post-card:only-child) {
        grid-template-columns: 1fr;
        max-width: 500px;
        margin: 0 auto;
    }
}

/* Responsive pour la section de filtrage et la grille */
@media (max-width: 768px) {
    .filter-section {
        padding: 1rem 0;
    }
    
    .filter-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .filter-label {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
    
    .filter-option-simple {
        width: 100%;
        justify-content: flex-start;
    }
    
    .posts-grid-simple {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        min-height: 300px;
    }
    
    .post-content {
        padding: 1rem;
    }
    
    .post-title {
        font-size: 1.1rem;
    }
    
    .post-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .filter-option-simple {
        max-width: 100%;
    }
    
    .posts-grid-simple {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
        min-height: 250px;
    }
    
    .post-content {
        padding: 0.75rem;
    }
    
    .post-title {
        font-size: 1rem;
    }
}

/* Styles pour la page newsletter individuelle */
.back-link {
    display: inline-block;
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
    margin-bottom: 2rem;
    transition: color 0.3s ease;
}

.back-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

.newsletter-single {
    padding: 2rem 0;
}

.newsletter-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    padding: 3rem;
    max-width: 800px;
    margin: 0 auto;
}

.newsletter-header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.newsletter-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.newsletter-date {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.newsletter-title {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
    margin: 0;
}

.newsletter-body {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #333;
    margin-bottom: 2rem;
}

.newsletter-body h1,
.newsletter-body h2,
.newsletter-body h3,
.newsletter-body h4,
.newsletter-body h5,
.newsletter-body h6 {
    color: #333;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.newsletter-body p {
    margin-bottom: 1.5rem;
}

.newsletter-body img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 1.5rem 0;
}

.newsletter-body a {
    color: #007bff;
    text-decoration: none;
}

.newsletter-body a:hover {
    text-decoration: underline;
}

.newsletter-footer {
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

.newsletter-info {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

/* Responsive pour la newsletter */
@media (max-width: 768px) {
    .newsletter-content {
        padding: 2rem;
        margin: 0 1rem;
    }
    
    .newsletter-title {
        font-size: 1.5rem;
    }
    
    .newsletter-body {
        font-size: 1rem;
    }
    
    .newsletter-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .newsletter-content {
        padding: 1.5rem;
    }
    
    .newsletter-title {
        font-size: 1.3rem;
    }
}

/* Styles pour la section des archives de newsletter */
.newsletter-archive-section {
    background: #f8f9fa;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-top: 1px solid #e9ecef;
}

.newsletter-archive-section .section-title {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 1.5rem;
    text-align: center;
}

.newsletter-archive-content {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Styles pour le contenu généré par le shortcode newsletter */
.newsletter-archive-content ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.newsletter-archive-content li {
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
    margin: 0;
}

.newsletter-archive-content li:last-child {
    border-bottom: none;
}

.newsletter-archive-content a {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
    font-size: 1.1rem;
    transition: color 0.3s ease;
}

.newsletter-archive-content a:hover {
    color: #0056b3;
    text-decoration: underline;
}

.newsletter-archive-content .date {
    color: #666;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    display: block;
}

/* Styles spécifiques pour la page International Advisory Board */
/* Cibler spécifiquement les blocs media-text de la page advisory board */
.wp-block-media-text.is-stacked-on-mobile {
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.wp-block-media-text.is-stacked-on-mobile:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

/* Style de l'image dans les blocs media-text */
.wp-block-media-text.is-stacked-on-mobile .wp-block-media-text__media {
    border-radius: 8px;
    overflow: hidden;
    margin-right: 2rem;
}

.wp-block-media-text.is-stacked-on-mobile .wp-block-media-text__media img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.wp-block-media-text.is-stacked-on-mobile:hover .wp-block-media-text__media img {
    transform: scale(1.05);
}

/* Style du contenu textuel */
.wp-block-media-text.is-stacked-on-mobile .wp-block-media-text__content {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

/* Style des noms et affiliations */
.wp-block-media-text.is-stacked-on-mobile .wp-block-media-text__content p:first-child {
    margin-bottom: 1rem;
}

.wp-block-media-text.is-stacked-on-mobile .wp-block-media-text__content p:first-child strong {
    font-size: 1.4rem;
    color: #333;
    font-weight: 600;
    display: block;
    margin-bottom: 0.5rem;
}

/* Style des détails (More information) */
.wp-block-media-text.is-stacked-on-mobile .wp-block-details {
    margin-top: 1rem;
    border: none;
    background: none;
    padding: 0;
}

.wp-block-media-text.is-stacked-on-mobile .wp-block-details summary {
    color: #007bff;
    font-weight: 500;
    cursor: pointer;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
    transition: color 0.3s ease;
    font-size: 1.1rem;
}

.wp-block-media-text.is-stacked-on-mobile .wp-block-details summary:hover {
    color: #0056b3;
}

.wp-block-media-text.is-stacked-on-mobile .wp-block-details summary em {
    font-style: italic;
    font-size: 1rem;
}

/* Style du contenu des détails */
.wp-block-media-text.is-stacked-on-mobile .wp-block-details p {
    margin-top: 1rem;
    line-height: 1.7;
    color: #555;
    font-size: 1rem;
}

/* Style spécifique pour le lien ORCID */
.wp-block-media-text.is-stacked-on-mobile .wp-block-details a {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.wp-block-media-text.is-stacked-on-mobile .wp-block-details a:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* Responsive pour les blocs media-text */
@media (max-width: 768px) {
    .wp-block-media-text.is-stacked-on-mobile {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .wp-block-media-text.is-stacked-on-mobile .wp-block-media-text__media {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .wp-block-media-text.is-stacked-on-mobile .wp-block-media-text__content p:first-child strong {
        font-size: 1.2rem;
    }
    
    .wp-block-media-text.is-stacked-on-mobile .wp-block-details summary {
        font-size: 1rem;
    }
    
    .wp-block-media-text.is-stacked-on-mobile .wp-block-details summary em {
        font-size: 0.95rem;
    }
    
    .wp-block-media-text.is-stacked-on-mobile .wp-block-details p {
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .wp-block-media-text.is-stacked-on-mobile {
        padding: 1rem;
    }
    
    .wp-block-media-text.is-stacked-on-mobile .wp-block-media-text__content p:first-child strong {
        font-size: 1.1rem;
    }
    
    .wp-block-media-text.is-stacked-on-mobile .wp-block-details summary {
        font-size: 0.95rem;
    }
    
    .wp-block-media-text.is-stacked-on-mobile .wp-block-details summary em {
        font-size: 0.9rem;
    }
    
    .wp-block-media-text.is-stacked-on-mobile .wp-block-details p {
        font-size: 0.9rem;
    }
}

.posts-grid article h2,
.posts-grid-simple .post-card h2,
.tease h2 {
    text-align: left;
    width: 100%;
    margin: 0 0 1rem 0;
    padding: 0;
    box-sizing: border-box;
}

/* Styles pour le formulaire de newsletter */
.tnp-subscription:hover {
    border-color: none;
    box-shadow: none;
    transform: none;
}

.tnp-subscription form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.tnp-subscription label {
    font-weight: 400 !important;
    color: #333 !important;
    font-size: 1rem !important;
    margin-bottom: 0.5rem;
}

.tnp-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.tnp-privacy-field {
    text-align: left !important;
    margin-bottom: 1% !important;
    height: 18px;
    flex-shrink: 0;
}

.tnp-subscription input[type="text"],
.tnp-subscription input[type="email"] {
    border: 1px solid #ddd !important;
    border-radius: 6px !important;
    font-size: 1rem !important;
    color: #000 !important;
}

.tnp-submit {
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-family: 'Barlow', sans-serif;
    box-shadow: 0 4px 15px rgba(56, 92, 169, 0.3);
}

.tnp-submit:hover {
    transform: translateY(-2px);
}

.tnp-submit:active {
    transform: translateY(0);
}

/* Responsive pour le formulaire de newsletter */
@media (max-width: 768px) {
    .tnp-subscription {
        padding: 2rem;
        margin: 1.5rem auto;
    }
    
    .tnp-field label {
        font-size: 1rem;
    }
    
    .tnp-field input[type="text"],
    .tnp-field input[type="email"] {
        padding: 0.875rem 1rem;
        font-size: 1rem;
    }
    
    .tnp-submit {
        padding: 1rem 1.5rem;
        font-size: 1.1rem;
    }
    
    .tnp-privacy-field {
        padding: 1rem;
    }
    
    .tnp-privacy-field label {
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .tnp-subscription {
        padding: 1.5rem;
        margin: 1rem auto;
    }
    
    .tnp-field input[type="text"],
    .tnp-field input[type="email"] {
        padding: 0.75rem 0.875rem;
        font-size: 0.95rem;
    }
    
    .tnp-submit {
        padding: 0.875rem 1.25rem;
        font-size: 1rem;
    }
    
    .tnp-privacy-field label {
        font-size: 0.9rem;
    }
}
