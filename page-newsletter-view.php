<?php
/**
 * Template Name: Newsletter View
 * Template pour afficher une newsletter individuelle
 */

$context = Timber::context();

// Récupérer l'ID de la newsletter depuis l'URL
$newsletter_id = isset($_GET['newsletter_id']) ? intval($_GET['newsletter_id']) : 0;

if ($newsletter_id && class_exists('Newsletter')) {
    global $wpdb;
    $newsletter_table = $wpdb->prefix . 'newsletter';

    $newsletter = $wpdb->get_row($wpdb->prepare(
        "SELECT id, subject, message, created, status
         FROM {$newsletter_table}
         WHERE id = %d AND status = 'sent'",
        $newsletter_id
    ));

    if ($newsletter) {
        // Créer le contexte pour la newsletter
        $context['newsletter'] = array(
            'id' => $newsletter->id,
            'title' => $newsletter->subject,
            'content' => $newsletter->message,
            'date' => date('d/m/Y', strtotime($newsletter->created)),
            'raw_date' => $newsletter->created
        );

        // Fil d'Ariane
        $context['breadcrumbs'] = function_exists('get_breadcrumb') ? get_breadcrumb() : '';
        
        // Langue actuelle
        $context['current_language'] = function_exists('pll_current_language') ? pll_current_language() : 'fr';

        // Rendre le template
        Timber::render('newsletter-single.twig', $context);
    } else {
        // Newsletter non trouvée, rediriger vers les actualités
        wp_redirect(home_url('/category/news-and-events/'));
        exit;
    }
} else {
    // Pas d'ID ou plugin non disponible, rediriger vers les actualités
    wp_redirect(home_url('/category/news-and-events/'));
    exit;
}
?>
