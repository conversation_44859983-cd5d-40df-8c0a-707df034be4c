parameters:
	ignoreErrors:
		- # The method is dynamically generated by the CheckSecurityNode
			message: '#^Call to an undefined method Twig\\Template\:\:checkSecurity\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: src/Extension/CoreExtension.php

		- # 2 parameters will be required
			message: '#^Method Twig\\Node\\IncludeNode\:\:addGetTemplate\(\) invoked with 2 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: src/Node/IncludeNode.php

		- # int|string will be supported in 4.x
			message: '#^PHPDoc tag @param for parameter $name with type int|string is not subtype of native type string\.$#'
			identifier: parameter.phpDocType
			count: 5
			path: src/Node/Node.php

		- # Adding 0 to the string representation of a number is valid and what we want here
			message: '#^Binary operation "\+" between 0 and string results in an error\.$#'
			identifier: binaryOp.invalid
			count: 1
			path: src/Lexer.php
