<?php
/**
 * Template Name: Page Consortium
 */

$context = Timber::context();

// Récupérer tous les champs ACF
$acf_fields = get_fields($post->ID);

// Initialiser le tableau des organisations
$context['grille_organisations'] = array();

if (isset($acf_fields['grille_organisations']) && is_array($acf_fields['grille_organisations'])) {
    foreach ($acf_fields['grille_organisations'] as $organisation) {
        $context['grille_organisations'][] = array(
            'organisation_url' => $organisation['organisation_url'] ?? '',
            'logo' => is_array($organisation['logo']) ? $organisation['logo']['url'] : ($organisation['logo'] ?? ''),
            'titre' => $organisation['titre'] ?? '',
        );
    }
}

Timber::render('page-consortium.twig', $context);